import React from 'react';
import { AssessmentInsights, getReadinessLevel, getReadinessColor } from '@/lib/assessmentScoring';

interface AssessmentResultsProps {
  insights: AssessmentInsights;
}

const AssessmentResults: React.FC<AssessmentResultsProps> = ({ insights }) => {
  const { scores, primaryMotivation, topSkills, biggestObstacles, recommendedTimeline, keyRecommendations, careerPathSuggestions } = insights;
  
  const readinessLevel = getReadinessLevel(scores.readinessScore);
  const readinessColor = getReadinessColor(scores.readinessScore);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Your Career Transition Assessment Results
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Based on your responses, here's your personalized career transition roadmap
        </p>
      </div>

      {/* Overall Readiness Score */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          Overall Readiness Score
        </h2>
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <div className="flex justify-between items-center mb-2">
              <span className={`text-xl font-bold ${readinessColor}`}>
                {readinessLevel}
              </span>
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {scores.readinessScore}/100
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-500 ${
                  scores.readinessScore >= 80 ? 'bg-green-500' :
                  scores.readinessScore >= 60 ? 'bg-yellow-500' :
                  scores.readinessScore >= 40 ? 'bg-orange-500' : 'bg-red-500'
                }`}
                style={{ width: `${scores.readinessScore}%` }}
              ></div>
            </div>
          </div>
        </div>
        <p className="mt-3 text-gray-600 dark:text-gray-400">
          {recommendedTimeline}
        </p>
      </div>

      {/* Detailed Scores */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          Detailed Assessment
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-700 dark:text-gray-300">Financial Readiness</span>
              <span className="font-semibold">{scores.financialReadiness}/5</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700 dark:text-gray-300">Risk Tolerance</span>
              <span className="font-semibold">{scores.riskTolerance}/5</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700 dark:text-gray-300">Support System</span>
              <span className="font-semibold">{scores.supportLevel}/5</span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-700 dark:text-gray-300">Skills Confidence</span>
              <span className="font-semibold">{scores.skillsConfidence}/100</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700 dark:text-gray-300">Urgency Level</span>
              <span className="font-semibold">{scores.urgencyLevel}/5</span>
            </div>
          </div>
        </div>
      </div>

      {/* Key Recommendations */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          Key Recommendations
        </h2>
        <ul className="space-y-2">
          {keyRecommendations.map((recommendation, index) => (
            <li key={index} className="flex items-start space-x-2">
              <span className="text-blue-500 mt-1">•</span>
              <span className="text-gray-700 dark:text-gray-300">{recommendation}</span>
            </li>
          ))}
        </ul>
      </div>

      {/* Career Path Suggestions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          Suggested Career Paths
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {careerPathSuggestions.map((path, index) => (
            <div
              key={index}
              className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 text-center"
            >
              <span className="text-blue-700 dark:text-blue-300 font-medium">{path}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Your Profile Summary */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          Your Profile Summary
        </h2>
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Primary Motivation</h3>
            <p className="text-gray-700 dark:text-gray-300 capitalize">
              {primaryMotivation.replace(/_/g, ' ')}
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Top Skills</h3>
            <div className="flex flex-wrap gap-2">
              {topSkills.map((skill, index) => (
                <span
                  key={index}
                  className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-3 py-1 rounded-full text-sm"
                >
                  {skill.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              ))}
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Main Challenges</h3>
            <div className="flex flex-wrap gap-2">
              {biggestObstacles.map((obstacle, index) => (
                <span
                  key={index}
                  className="bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 px-3 py-1 rounded-full text-sm"
                >
                  {obstacle.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
        <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          Next Steps
        </h2>
        <div className="space-y-3">
          <p className="text-gray-700 dark:text-gray-300">
            Based on your assessment, here's what we recommend you focus on next:
          </p>
          <ol className="list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-300">
            <li>Review the career paths that align with your skills and interests</li>
            <li>Start working on your biggest obstacles, beginning with the most critical ones</li>
            <li>Create a timeline based on your readiness level and urgency</li>
            <li>Build your support network and seek mentorship in your target field</li>
            <li>Consider retaking this assessment in 3-6 months to track your progress</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default AssessmentResults;
