import React from 'react';

interface TextQuestionProps {
  questionKey: string;
  placeholder?: string;
  maxLength?: number;
  minLength?: number;
  currentValue: string | null;
  onChange: (questionKey: string, value: string) => void;
}

const TextQuestion: React.FC<TextQuestionProps> = ({
  questionKey,
  placeholder,
  maxLength = 1000,
  minLength = 0,
  currentValue,
  onChange,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = event.target.value;
    onChange(questionKey, value);
  };

  const characterCount = currentValue?.length || 0;
  const isNearLimit = maxLength && characterCount > maxLength * 0.8;

  return (
    <div className="space-y-2">
      <textarea
        id={questionKey}
        name={questionKey}
        value={currentValue || ''}
        onChange={handleChange}
        placeholder={placeholder}
        maxLength={maxLength}
        minLength={minLength}
        rows={4}
        className={`
          w-full px-3 py-2 border rounded-md shadow-sm resize-y min-h-[100px]
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100
          dark:placeholder-gray-400 dark:focus:ring-blue-600 dark:focus:border-blue-600
          ${currentValue ? 'border-gray-300' : 'border-gray-300'}
        `}
      />
      
      {maxLength && (
        <div className={`text-sm text-right ${
          isNearLimit 
            ? 'text-orange-600 dark:text-orange-400' 
            : 'text-gray-500 dark:text-gray-400'
        }`}>
          {characterCount}/{maxLength} characters
        </div>
      )}
      
      {minLength > 0 && currentValue && currentValue.length < minLength && (
        <div className="text-sm text-red-600 dark:text-red-400">
          Minimum {minLength} characters required
        </div>
      )}
    </div>
  );
};

export default TextQuestion;
