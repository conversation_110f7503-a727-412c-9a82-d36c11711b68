# FAAFO Career Transition Platform - Enhanced Technical Specifications

## 1. Introduction

This document provides comprehensive technical specifications for the FAAFO Career Transition Platform, defining the technology stack, development standards, infrastructure requirements, and operational procedures for building a scalable, secure, and maintainable career transition platform. While this document outlines the enhanced target specifications, specific notes will indicate simplifications for the initial Minimum Viable Product (MVP).

## 2. Technology Stack & Versions

### 2.1. Core Runtime & Framework
- **Runtime Environment:** Node.js `v20.x` (LTS)
- **Core Framework:** Next.js `v14.2.5` (App Router) - *(Previously v15.x. Downgraded due to compatibility issues during initial development)*
- **Language:** TypeScript `v5.3+`
- **Package Manager:** pnpm `v8.x`

### 2.2. Database & ORM
- **Primary Database:** PostgreSQL `v16.x`
  - *Managed Services (MVP & Production):* Vercel Postgres or Neon (serverless, auto-scaling).
- **ORM:** Prisma `v5.8+`
- **Database Migrations:** Prisma Migrate with seed scripts (MVP: Basic seed script for static career paths, executed via `tsx prisma/seed.ts` configured in `package.json`).
- **Connection Pooling:** Built-in Prisma connection pooling. *(MVP Note: PgBouncer is Post-MVP if high traffic dictates).*

### 2.3. Caching & Performance
- **Application Cache (Post-MVP):** Redis `v7.x`
  - *Managed Services:* Vercel KV, Upstash Redis, or Google Cloud Memorystore.
- **Client Library (Post-MVP):** `ioredis` `v5.x`.
- **CDN (MVP):** Vercel Edge Network (automatic for static assets). *(MVP Note: Custom caching strategies are Post-MVP).*
- **Session Storage (MVP - if needed by Auth.js strategy):** Redis-based (e.g., Vercel KV) or database sessions.

### 2.4. Authentication & Authorization
- **Primary:** Auth.js (NextAuth.js `v4.24.11`) - *(Previously v5. Downgraded due to compatibility issues and to align with common v4 patterns for initial setup)*.
- **Environment Variables Required:** `NEXTAUTH_URL`, `NEXTAUTH_SECRET`.
- **Providers:**
  - **MVP:** Email/Password. *(MVP Note: Magic links are a good enhancement but can be Post-MVP if initial email/pass is simpler).*
  - **Post-MVP:** Google OAuth 2.0, LinkedIn OAuth.
- **Security Features (MVP Focus):**
  - JWT handling (via Auth.js).
  - Rate limiting for auth endpoints (basic).
  - Account lockout policies.
- **Security Features (Post-MVP):**
  - Refresh tokens (if not default with chosen Auth.js strategy).
  - Multi-factor authentication.

### 2.5. Assessment System & Intelligence Engine ✅ IMPLEMENTED
- **MVP ✅ IMPLEMENTED:** Comprehensive rule-based intelligence system with:
  - **Multi-dimensional scoring algorithms** for career transition readiness assessment
  - **Personalized insights generation** based on 20+ assessment data points
  - **Career path recommendation engine** using skills, values, and preference matching
  - **Timeline guidance algorithms** based on financial readiness and urgency factors
  - **Obstacle identification and recommendation system** for personalized next steps
- **Assessment Architecture:**
  - **Question Types:** Multiple choice (single/multi-select), scale questions (1-5), open-ended text responses
  - **Data Storage:** Flexible JSON storage in PostgreSQL for diverse answer types
  - **Scoring Engine:** TypeScript-based algorithms in `src/lib/assessmentScoring.ts`
  - **Results Presentation:** Professional results page with visual score displays and actionable insights
- **Post-MVP AI Integration:** Google Gemini API (`gemini-1.5-pro` model) for:
  - Enhanced analysis of open-ended text responses
  - Dynamic content generation for career paths
  - AI-powered coaching suggestions and personalized action plans
- **Post-MVP Client Library:** `@google/generative-ai` latest stable.
- **Post-MVP Fallback:** OpenAI GPT-4 API.

### 2.6. File Storage & Media
- **MVP:** No direct user file uploads (e.g., resumes, profile images unless extremely simple implementation).
- **Post-MVP Primary:** Vercel Blob Storage.
- **Post-MVP Alternative:** Google Cloud Storage with CDN.
- **Post-MVP Client Libraries:** `@vercel/blob`, `@google-cloud/storage`.
- **Post-MVP File Types:** PDFs (resumes), images (profiles), documents.
- **Post-MVP Security:** Signed URLs, virus scanning, file type validation.

### 2.7. External Integrations
- **MVP:** Basic email service for auth flows (e.g., Resend/SendGrid via Auth.js). Vercel Analytics.
- **Post-MVP (Future):** Plaid API, Job Board APIs (LinkedIn, Indeed), Background Checks (Checkr API), Payment Processing (Stripe API).

### 2.8. Frontend Architecture
- **UI Framework:** React `v18.2+`.
- **Styling:** Tailwind CSS `v3.4+`.
- **Component Library:**
  - **Primary:** shadcn/ui (Radix UI + Tailwind).
  - **Icons:** Lucide React, Heroicons.
- **State Management:**
  - **Global State (MVP):** React Context API for very simple needs, or Zustand `v4.x` if slightly more complexity arises early.
  - **Server State:** TanStack Query `v5.x` (React Query).
  - **Form State:** React Hook Form `v7.x` + Zod validation.
- **Data Fetching:**
  - Server Components for initial data.
  - TanStack Query for client-side data fetching and mutations.
  - Server Actions for form submissions.

### 2.9. Development Tools & Quality
- **Build Tool:** Next.js CLI (`next build`). *(MVP Note: Turbopack is used by `next dev` by default, `next build` uses Webpack unless Turbopack for builds is stable and default).*
- **TypeScript Execution:** `tsx` for running .ts scripts (e.g., seed scripts).
- **Linting:** ESLint `v8.x` with custom config (e.g., `@typescript-eslint/parser`, `eslint-plugin-react-hooks`, `eslint-plugin-jsx-a11y`).
- **Formatting:** Prettier `v3.x` with import sorting.
- **Type Checking:** TypeScript strict mode.
- **Pre-commit (Post-MVP):** Husky + lint-staged. *(MVP: Manual checks or simple Git hooks).*

### 2.10. Testing Strategy
- **MVP:**
    - **Unit Testing:** Vitest `v1.x` for critical utility functions and core logic including:
      - Freedom Fund calculation algorithms
      - **Assessment scoring algorithms** (`calculateAssessmentScores`, `generateAssessmentInsights`)
      - **Career path recommendation logic** with various skill/value combinations
      - **Question validation and data transformation** functions
      - **Insights generation** with edge cases and boundary conditions
    - **Manual Component/UI Testing:** Thorough manual testing of all MVP features including:
      - **Complete assessment flow** through all 6 steps
      - **Question type rendering** (multiple choice, scale, text)
      - **Progress saving and resumption** functionality
      - **Results page display** with various score combinations
      - Cross-browser compatibility for assessment interface
- **Post-MVP:**
    - **Unit Testing:** Vitest `v1.x` (target 80% coverage for critical paths).
    - **Component Testing:** React Testing Library `v14.x`.
    - **API Testing:** Supertest for API routes.
    - **E2E Testing:** Playwright `v1.x`.
    - **Visual Testing (Future):** Chromatic or Percy.

### 2.11. Monitoring & Observability
- **MVP:**
    - **Application Monitoring:** Vercel Analytics (default).
    - **Logging:** Console logging in development, Vercel function logs in production.
- **Post-MVP:**
    - **Application Monitoring:** Sentry for error tracking.
    - **Performance:** Web Vitals tracking, Real User Monitoring (beyond Vercel defaults).
    - **Logging:** Structured JSON logs with Winston for production.
    - **Uptime Monitoring:** External service (Pingdom/UptimeRobot).

## 3. Assessment System Technical Implementation ✅ COMPLETED

### 3.1. Assessment Architecture
The career assessment system is built as a comprehensive, multi-step questionnaire with sophisticated scoring and insights generation:

**Core Components:**
- **Assessment Definition** (`src/lib/assessmentDefinition.ts`): TypeScript interfaces and configuration for 6-step assessment
- **Scoring Engine** (`src/lib/assessmentScoring.ts`): Rule-based algorithms for multi-dimensional analysis
- **UI Components** (`src/components/assessment/`): Reusable question components with consistent styling
- **API Endpoints** (`src/app/api/assessment/`): RESTful endpoints for saving progress and retrieving results
- **Results Presentation** (`src/app/assessment/results/`): Professional results page with visual insights

### 3.2. Question Types & Data Models
```typescript
// Supported question types with flexible validation
interface MCQuestion extends BaseQuestion {
  type: 'multipleChoice';
  options: Option[];
  allowMultiple?: boolean;
  required?: boolean;
}

interface ScQuestion extends BaseQuestion {
  type: 'scale';
  minLabel: string;
  maxLabel: string;
  numberOfSteps: number;
  required?: boolean;
}

interface TextQuestion extends BaseQuestion {
  type: 'text';
  placeholder?: string;
  maxLength?: number;
  minLength?: number;
  required?: boolean;
}
```

### 3.3. Scoring Algorithm Architecture
**Multi-dimensional Assessment Scoring:**
- **Readiness Score** (0-100): Weighted combination of financial comfort, confidence, support, and skills
- **Risk Tolerance** (1-5): Direct assessment of user's comfort with career-related uncertainty
- **Urgency Level** (1-5): Calculated from timeline preferences and current situation
- **Skills Confidence** (0-100): Based on number and quality of selected skills
- **Support Level** (1-5): Assessment of available support network
- **Financial Readiness** (1-5): Direct assessment of financial comfort for transition

### 3.4. Career Path Recommendation Engine
**Rule-based Matching System:**
- Skills-based recommendations using predefined skill-to-career mappings
- Values alignment analysis for work environment and autonomy preferences
- Obstacle-aware suggestions that consider identified barriers
- Dynamic suggestion generation with duplicate removal and prioritization

### 3.5. Data Storage Strategy
**Flexible JSON Storage:**
- Assessment responses stored as JSON in PostgreSQL for maximum flexibility
- Support for string, string[], number, and null value types
- Indexed by assessment ID for efficient retrieval
- Maintains response history with timestamps for future progress tracking

## 4. Enhanced Coding Standards
*(No changes needed here from your version, these standards are excellent and apply from MVP onwards.)*

### 3.1. TypeScript Configuration
```typescript
// tsconfig.json highlights
{
  "compilerOptions": {
    "strict": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true, // Consider if too strict for initial MVP, can enable later
    "exactOptionalPropertyTypes": true // Consider if too strict for initial MVP
  }
}
Use code with caution.
Markdown
(MVP Note: noUncheckedIndexedAccess and exactOptionalPropertyTypes are very strict; enable them if you are comfortable, otherwise, they can be post-MVP strictness additions.)

3.2. Code Organization
(No changes needed, good structure for MVP too.)

3.3. Naming Conventions
(No changes needed.)

3.4. API Standards
(No changes needed, apply from MVP.)

3.5. Error Handling
(No changes needed, apply from MVP.)

4. Security Framework
(MVP will implement foundational security. Advanced items are Post-MVP.)

4.1. Authentication Security (MVP Focus)
Password requirements (as per REQ-AUTH-006).
Basic rate limiting on login/register.
Account lockout (as per REQ-AUTH-005).
Secure cookie settings for sessions (SameSite=Strict).
CSRF protection (Next.js/Auth.js defaults).
(Post-MVP: More intelligent rate limiting, MFA, advanced session management if needed).
4.2. Data Protection (MVP Focus)
Input validation (Zod schemas) for all MVP inputs.
SQL injection prevention (Prisma by default).
XSS prevention (Next.js defaults, careful output encoding).
(Post-MVP: CSP headers, advanced file upload security).
4.3. Infrastructure Security (MVP Focus)
HTTPS only (Vercel default).
Environment variables for all secrets (Vercel env vars).
Database SSL connections (managed service default).
(Post-MVP: API key rotation policies).
5. Performance Optimization
(MVP focuses on good defaults and sensible component design.)

5.1. Frontend Performance (MVP Focus)
Next.js Image component for any images used.
Sensible code splitting by route (Next.js default).
Aim for good Core Web Vitals from the start with efficient components.
(Post-MVP: Aggressive caching, bundle analysis, advanced font optimization).
5.2. Backend Performance (MVP Focus)
Efficient Prisma queries for MVP features.
Ensure API responses are not unnecessarily large.
(Post-MVP: Database indexing strategy reviews, Redis caching, response compression if not default).
6. Deployment & Infrastructure
(MVP leverages Vercel's streamlined deployment.)

6.1. Environment Strategy
Development: Local Next.js dev server.
Staging/Preview: Vercel preview deployments per PR/feature branch.
Production: Vercel connected to the main branch.
(Post-MVP: Docker Compose for local complex services, if any.)
6.2. CI/CD Pipeline
MVP:
GitHub for source control.
Vercel for automated builds and deployments from connected GitHub repository.
Local/manual linting, formatting, type-checking before push.
Post-MVP (GitHub Actions Workflow as described in your doc):
Code quality checks, security scanning, automated testing, performance budgets, automated deployment to staging/production.
6.3. Database Management
MVP: `prisma migrate dev` locally, `prisma migrate deploy` for Vercel deployments (can be run via Vercel build step or manually before deploying app changes). Basic seed script for static career paths, executed via `npx prisma db seed` which uses the `tsx prisma/seed.ts` command configured in `package.json`'s `prisma.seed` field.
Post-MVP: Daily automated backups with point-in-time recovery (often a feature of managed DB services), more extensive seeding.
7. Scalability Considerations
(MVP relies on Vercel's inherent serverless scalability.)

7.1. Horizontal Scaling (MVP)
Next.js API routes on Vercel scale automatically.
Managed PostgreSQL (Vercel Postgres/Neon) handles initial scaling needs.
(Post-MVP: Read replicas, distributed Redis).
7.2. Performance Monitoring (MVP)
Vercel Analytics for basic metrics.
Monitor Vercel function logs for errors or extreme slowness.
(Post-MVP: Detailed metrics, alerts, capacity planning).
8. Compliance & Privacy
(MVP focuses on foundational privacy aspects.)

8.1. Data Privacy (MVP)
Clear, user-facing Privacy Policy.
Adherence to data minimization for MVP features.
Secure handling of collected PII.
(Post-MVP: Features for GDPR/CCPA data subject rights, consent management dashboards).
8.2. Accessibility (MVP)
Use semantic HTML.
Ensure keyboard navigability for MVP features.
Test with a screen reader for core flows.
Aim for good color contrast.
(Post-MVP: Full WCAG 2.1 AA compliance drive).
9. Documentation Standards
(Applies from MVP onwards, but depth might increase Post-MVP.)

9.1. Code Documentation
JSDoc/TSDoc for public APIs and complex utility functions in MVP.
README for project setup.
9.2. Architecture Documentation
These project-docs serve as the primary architecture documentation.
Simple ERD for MVP database schema.
10. Maintenance & Updates
(MVP focuses on keeping the core stack secure.)

10.1. Dependency Management (MVP)
Periodically review and update critical dependencies (Next.js, Prisma, Auth.js).
Apply security patches as notified by pnpm audit or GitHub Dependabot.
(Post-MVP: Monthly comprehensive updates, stricter evaluation of breaking changes).
10.2. Technical Debt Management (MVP)
Strive for clean code from the start.
Self-review code before committing/merging.
(Post-MVP: Formal code reviews if team grows, dedicated refactoring sprints, performance audits).
