import '@testing-library/jest-dom';

// Mock Next.js server runtime objects for testing
class MockNextRequest extends Request {
  constructor(input: RequestInfo, init?: RequestInit) {
    super(input, init);
  }
}

class MockNextResponse extends Response {
  constructor(body?: BodyInit | null, init?: ResponseInit) {
    super(body, init);
  }

  static json(data: unknown, init?: ResponseInit) {
    return new MockNextResponse(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      ...init,
    });
  }

  static redirect(url: string | URL, status: number = 307) {
    return new MockNextResponse(null, { status, headers: { Location: url.toString() } });
  }

  static error() {
    return new MockNextResponse('Internal Server Error', { status: 500 });
  }
}

declare global {
  var NextRequest: typeof MockNextRequest;
  var NextResponse: typeof MockNextResponse;
}

global.NextRequest = MockNextRequest;
global.NextResponse = MockNextResponse; 