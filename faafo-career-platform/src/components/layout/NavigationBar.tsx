'use client'; // Required for useSession hook

import Link from 'next/link';
import { useSession, signIn, signOut } from 'next-auth/react';
import { useTheme } from 'next-themes';
import { Compass, HomeIcon, Settings2, User, LogOut, LogIn, Moon, UserPlus, Sun, HelpCircle, MessageSquare, Briefcase, BookOpen } from 'lucide-react';

export function NavigationBar() {
  const { status } = useSession();
  const isAuthenticated = status === 'authenticated';
  const { theme, setTheme } = useTheme();

  return (
    <header className="sticky top-0 z-50 bg-background dark:bg-card shadow-sm dark:shadow-gray-700">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" aria-label="Main navigation">
        <div className="w-full py-3 flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="flex items-center text-gray-900 dark:text-gray-100">
              <Compass className="h-8 w-8 mr-2 text-indigo-600 dark:text-indigo-400" /> 
              <span className="text-2xl font-bold">FAAFO</span>
            </Link>
          </div>
          <div className="ml-10 space-x-4 flex items-center">
            <Link href="/" className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-2 py-1 inline-flex items-center space-x-1">
              <HomeIcon className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              <span>Home</span>
            </Link>
            <Link href="/career-paths" className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-2 py-1 inline-flex items-center space-x-1">
              <Briefcase className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              <span>Career Paths</span>
            </Link>
            <Link href="/resources" className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-2 py-1 inline-flex items-center space-x-1">
              <BookOpen className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              <span>Resources</span>
            </Link>
            <Link href="/help" className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-2 py-1 inline-flex items-center space-x-1">
              <HelpCircle className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              <span>Help</span>
            </Link>

            {isAuthenticated ? (
              <>
                <Link href="/forum" className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-2 py-1 inline-flex items-center space-x-1">
                  <MessageSquare className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  <span>Forum</span>
                </Link>
                <Link href="/profile" className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-2 py-1 inline-flex items-center space-x-1">
                  <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  <span>Profile</span>
                </Link>
                <button
                  onClick={() => signOut()}
                  className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-2 py-1 inline-flex items-center space-x-1"
                  aria-label="Sign out"
                >
                  <LogOut className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  <span>Sign Out</span>
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => signIn()}
                  className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-2 py-1 inline-flex items-center space-x-1"
                  aria-label="Log in"
                >
                  <LogIn className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  <span>Log In</span>
                </button>
                <Link
                  href="/signup"
                  className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-gray-800 hover:bg-gray-700 dark:bg-indigo-500 dark:hover:bg-indigo-400 transition-colors duration-150 ml-2"
                >
                  <UserPlus className="h-4 w-4 mr-1" /> Sign Up
                </Link>
              </>
            )}

            <button 
              aria-label="Toggle theme" 
              className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-2 py-1 ml-2 inline-flex items-center space-x-1"
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            >
              {theme === 'dark' ? (
                <Sun className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              ) : (
                <Moon className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              )}
              <span>{theme === 'dark' ? 'Light' : 'Dark'}</span>
            </button>
          </div>
        </div>
      </nav>
    </header>
  );
} 