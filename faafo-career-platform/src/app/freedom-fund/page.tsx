'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import FreedomFundCalculatorForm from '../../../components/freedom-fund/FreedomFundCalculatorForm';
import FreedomFundResults from '../../../components/freedom-fund/FreedomFundResults';

interface FreedomFundFormData {
  monthlyExpenses: number;
  coverageMonths: number;
  currentSavings?: number; // This is from the form
}

// Represents the data structure from/to the backend API
interface FreedomFundData extends FreedomFundFormData {
  id: string;
  userId: string;
  targetSavings: number;
  currentSavingsAmount?: number; // This is specifically for the DB model field name
  createdAt: string;
  updatedAt: string;
}

// API interaction functions
const fetchFreedomFundAPI = async (): Promise<FreedomFundData | null> => {
  const response = await fetch('/api/freedom-fund');
  if (response.status === 404) {
    return null;
  }
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Failed to load data' }));
    throw new Error(errorData.error || 'Failed to load Freedom Fund data');
  }
  return response.json();
};

const saveFreedomFundAPI = async (data: FreedomFundFormData): Promise<FreedomFundData> => {
  const response = await fetch('/api/freedom-fund', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Failed to save data' }));
    throw new Error(errorData.error || 'Failed to save Freedom Fund data');
  }
  return response.json();
};

export default function FreedomFundPage() {
  const { status: sessionStatus } = useSession();
  const router = useRouter();

  const [targetAmount, setTargetAmount] = useState<number | null>(null);
  const [currentSavingsDisplay, setCurrentSavingsDisplay] = useState<number | null>(null);
  const [initialFormData, setInitialFormData] = useState<Partial<FreedomFundFormData> | undefined>(undefined);
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (sessionStatus === 'authenticated') {
      setIsLoading(true);
      setError(null);
      fetchFreedomFundAPI()
        .then(data => {
          if (data) {
            setTargetAmount(data.targetSavings);
            // The form field is `currentSavings`, DB field is `currentSavingsAmount`
            setCurrentSavingsDisplay(data.currentSavingsAmount ?? null);
            setInitialFormData({
              monthlyExpenses: data.monthlyExpenses,
              coverageMonths: data.coverageMonths,
              currentSavings: data.currentSavingsAmount,
            });
          } else {
            // No existing data, clear any previous state
            setTargetAmount(null);
            setCurrentSavingsDisplay(null);
            setInitialFormData(undefined);
          }
        })
        .catch(err => {
          console.error('Error fetching Freedom Fund data:', err);
          setError(err.message || 'Could not load your saved data.');
          setTargetAmount(null); // Clear stale data on error
          setCurrentSavingsDisplay(null);
          setInitialFormData(undefined);
        })
        .finally(() => setIsLoading(false));
    } else if (sessionStatus === 'unauthenticated') {
      router.push('/login');
    }
  }, [sessionStatus, router]);

  const handleCalculateAndSave = useCallback(async (formData: FreedomFundFormData) => {
    setIsSaving(true);
    setError(null);
    try {
      const savedData = await saveFreedomFundAPI(formData);
      setTargetAmount(savedData.targetSavings);
      setCurrentSavingsDisplay(savedData.currentSavingsAmount ?? null);
      setInitialFormData({ // Update initial form data for consistency if form is re-rendered
        monthlyExpenses: savedData.monthlyExpenses,
        coverageMonths: savedData.coverageMonths,
        currentSavings: savedData.currentSavingsAmount,
      });
      alert('Freedom Fund data saved successfully!');
    } catch (apiError) {
      console.error('API Error saving Freedom Fund data:', apiError);
      const message = apiError instanceof Error ? apiError.message : 'An unknown error occurred.';
      setError(message);
      alert(`Error: ${message}`);
    }
    setIsSaving(false);
  }, []);

  if (sessionStatus === 'loading' || (sessionStatus === 'authenticated' && isLoading)) {
    return <p className="text-center py-10">Loading Freedom Fund data...</p>;
  }
  if (sessionStatus === 'unauthenticated') {
     // Should have been redirected by useEffect, but as a fallback:
    return <p className="text-center py-10">Please <a href="/login" className="underline">login</a> to use the Freedom Fund calculator.</p>;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <h1 className="text-3xl font-bold mb-6 text-center">Freedom Fund Calculator</h1>
      <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 text-center">
        Calculate your emergency savings target and track your progress towards financial freedom.
      </p>

      {error && <p className="my-4 text-center text-red-500 bg-red-100 dark:bg-red-900 dark:text-red-200 p-3 rounded-md" role="alert">Error: {error}</p>}

      <FreedomFundCalculatorForm 
        onSubmit={handleCalculateAndSave} 
        initialData={initialFormData} // Pass initial data to prefill form
        key={initialFormData ? JSON.stringify(initialFormData) : 'empty-form'} // Force re-render if initialData changes
      />

      {(targetAmount !== null || initialFormData) && (
        <FreedomFundResults 
          targetAmount={targetAmount} 
          currentSavings={currentSavingsDisplay} 
        />
      )}

      {isSaving && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-50 flex items-center justify-center z-50">
            <p className="text-white text-lg p-4 bg-gray-800 rounded-md">Saving data...</p>
        </div>
      )}

    </div>
  );
}
