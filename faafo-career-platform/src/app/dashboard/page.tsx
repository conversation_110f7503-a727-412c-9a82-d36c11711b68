'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  CheckCircle,
  Clock,
  DollarSign,
  MessageSquare,
  Briefcase,
  BookOpen,
  ArrowRight,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface DashboardStats {
  assessmentCompleted: boolean;
  freedomFundTarget?: number;
  freedomFundCurrent?: number;
  forumPosts: number;
  bookmarkedPaths: number;
}

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats>({
    assessmentCompleted: false,
    forumPosts: 0,
    bookmarkedPaths: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated') {
      fetchDashboardData();
    }
  }, [status, router]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Fetch assessment status
      const assessmentResponse = await fetch('/api/assessment');
      const assessmentData = await assessmentResponse.json();

      // Fetch freedom fund data
      const freedomFundResponse = await fetch('/api/freedom-fund');
      const freedomFundData = freedomFundResponse.ok ? await freedomFundResponse.json() : null;

      // Fetch forum posts (simplified - just get count)
      const forumResponse = await fetch('/api/forum/posts');
      const forumData = forumResponse.ok ? await forumResponse.json() : [];

      setStats({
        assessmentCompleted: assessmentData.status === 'COMPLETED',
        freedomFundTarget: freedomFundData?.targetSavings,
        freedomFundCurrent: freedomFundData?.currentSavingsAmount,
        forumPosts: forumData.length || 0,
        bookmarkedPaths: 0, // TODO: Implement bookmarked paths tracking
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading dashboard...</div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>Please <Link href="/login" className="text-blue-600 hover:underline">log in</Link> to access your dashboard.</p>
        </div>
      </div>
    );
  }

  const freedomFundProgress = stats.freedomFundTarget && stats.freedomFundCurrent
    ? (stats.freedomFundCurrent / stats.freedomFundTarget) * 100
    : 0;

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Welcome back, {session?.user?.name || session?.user?.email?.split('@')[0]}!
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Here's your career transition progress overview.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <div className="flex items-center gap-3">
            {stats.assessmentCompleted ? (
              <CheckCircle className="h-8 w-8 text-green-500" />
            ) : (
              <Clock className="h-8 w-8 text-orange-500" />
            )}
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Assessment</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {stats.assessmentCompleted ? 'Completed' : 'Pending'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <div className="flex items-center gap-3">
            <DollarSign className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Freedom Fund</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {stats.freedomFundTarget ? `${Math.round(freedomFundProgress)}%` : 'Not Set'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <div className="flex items-center gap-3">
            <MessageSquare className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Forum Activity</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {stats.forumPosts} posts
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <div className="flex items-center gap-3">
            <Briefcase className="h-8 w-8 text-purple-500" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Bookmarked Paths</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {stats.bookmarkedPaths}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Items */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Next Steps
          </h2>
          <div className="space-y-4">
            {!stats.assessmentCompleted && (
              <div className="flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-gray-100">Complete Assessment</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Get personalized career path recommendations</p>
                  </div>
                </div>
                <Button asChild size="sm">
                  <Link href="/assessment">Start</Link>
                </Button>
              </div>
            )}

            {!stats.freedomFundTarget && (
              <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="flex items-center gap-3">
                  <DollarSign className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-gray-100">Set Freedom Fund Goal</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Calculate your financial runway</p>
                  </div>
                </div>
                <Button asChild size="sm">
                  <Link href="/freedom-fund">Calculate</Link>
                </Button>
              </div>
            )}

            <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="flex items-center gap-3">
                <Briefcase className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-gray-100">Explore Career Paths</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Discover new opportunities</p>
                </div>
              </div>
              <Button asChild size="sm" variant="outline">
                <Link href="/career-paths">Explore</Link>
              </Button>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Resources & Support
          </h2>
          <div className="space-y-4">
            <Link
              href="/resources"
              className="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
            >
              <div className="flex items-center gap-3">
                <BookOpen className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-gray-100">Mindset Resources</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Overcome fears and build confidence</p>
                </div>
              </div>
              <ArrowRight className="h-4 w-4 text-gray-400" />
            </Link>

            <Link
              href="/forum"
              className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
            >
              <div className="flex items-center gap-3">
                <MessageSquare className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-gray-100">Community Forum</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Connect with others on similar journeys</p>
                </div>
              </div>
              <ArrowRight className="h-4 w-4 text-gray-400" />
            </Link>

            <Link
              href="/help"
              className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            >
              <div className="flex items-center gap-3">
                <TrendingUp className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-gray-100">Help & Guides</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Get help using the platform</p>
                </div>
              </div>
              <ArrowRight className="h-4 w-4 text-gray-400" />
            </Link>
          </div>
        </div>
      </div>

      {/* Freedom Fund Progress */}
      {stats.freedomFundTarget && (
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Freedom Fund Progress
            </h2>
            <Button asChild variant="outline" size="sm">
              <Link href="/freedom-fund">Update</Link>
            </Button>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                ${stats.freedomFundCurrent?.toLocaleString() || 0} of ${stats.freedomFundTarget.toLocaleString()}
              </span>
              <span className="text-gray-600 dark:text-gray-400">
                {Math.round(freedomFundProgress)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div
                className="bg-green-500 h-3 rounded-full transition-all duration-500"
                style={{ width: `${Math.min(freedomFundProgress, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}