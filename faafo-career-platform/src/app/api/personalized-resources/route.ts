import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const skillLevel = searchParams.get('skillLevel') || 'BEGINNER';

    // Get user's latest completed assessment
    const latestAssessment = await prisma.assessment.findFirst({
      where: {
        userId: session.user.id,
        status: 'COMPLETED'
      },
      include: {
        responses: true
      },
      orderBy: {
        completedAt: 'desc'
      }
    });

    if (!latestAssessment) {
      // If no assessment, return general beginner resources
      const generalResources = await prisma.learningResource.findMany({
        where: {
          isActive: true,
          skillLevel: 'BEGINNER',
          cost: {
            in: ['FREE', 'FREEMIUM']
          }
        },
        include: {
          careerPaths: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          ratings: {
            select: {
              rating: true
            }
          }
        },
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      });

      return NextResponse.json({
        success: true,
        data: {
          resources: generalResources.map(resource => ({
            ...resource,
            averageRating: resource.ratings.length > 0 
              ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length
              : 0,
            totalRatings: resource.ratings.length
          })),
          recommendationReason: 'General recommendations for beginners'
        }
      });
    }

    // Analyze assessment responses to determine interests
    const responses = latestAssessment.responses;
    const interests = analyzeAssessmentForInterests(responses);

    // Get career path suggestions based on assessment
    const suggestedCareerPaths = await getCareerPathSuggestions(latestAssessment.id);

    // Get resources based on suggested career paths and interests
    let recommendedResources = [];

    if (suggestedCareerPaths.length > 0) {
      // Get resources connected to suggested career paths
      recommendedResources = await prisma.learningResource.findMany({
        where: {
          isActive: true,
          skillLevel: skillLevel as any,
          careerPaths: {
            some: {
              id: {
                in: suggestedCareerPaths.map(cp => cp.id)
              }
            }
          }
        },
        include: {
          careerPaths: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          ratings: {
            select: {
              rating: true
            }
          }
        },
        take: limit,
        orderBy: [
          { cost: 'asc' }, // Prioritize free resources
          { createdAt: 'desc' }
        ]
      });
    }

    // If not enough resources from career paths, supplement with interest-based resources
    if (recommendedResources.length < limit) {
      const additionalResources = await prisma.learningResource.findMany({
        where: {
          isActive: true,
          skillLevel: skillLevel as any,
          category: {
            in: interests
          },
          id: {
            notIn: recommendedResources.map(r => r.id)
          }
        },
        include: {
          careerPaths: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          ratings: {
            select: {
              rating: true
            }
          }
        },
        take: limit - recommendedResources.length,
        orderBy: [
          { cost: 'asc' },
          { createdAt: 'desc' }
        ]
      });

      recommendedResources = [...recommendedResources, ...additionalResources];
    }

    // Add rating information
    const resourcesWithRatings = recommendedResources.map(resource => ({
      ...resource,
      averageRating: resource.ratings.length > 0 
        ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length
        : 0,
      totalRatings: resource.ratings.length
    }));

    return NextResponse.json({
      success: true,
      data: {
        resources: resourcesWithRatings,
        suggestedCareerPaths,
        interests,
        recommendationReason: suggestedCareerPaths.length > 0 
          ? `Based on your assessment results and interest in ${suggestedCareerPaths.map(cp => cp.name).join(', ')}`
          : 'Based on your assessment responses and interests'
      }
    });

  } catch (error) {
    console.error('Error fetching personalized resources:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch personalized resources' },
      { status: 500 }
    );
  }
}

function analyzeAssessmentForInterests(responses: any[]): string[] {
  const interests: string[] = [];
  
  // Analyze responses to determine interests
  responses.forEach(response => {
    const { questionKey, answerValue } = response;
    
    // Map assessment responses to learning categories
    if (questionKey === 'dissatisfaction_triggers') {
      if (Array.isArray(answerValue)) {
        if (answerValue.includes('lack_of_growth')) {
          interests.push('ARTIFICIAL_INTELLIGENCE', 'DATA_SCIENCE');
        }
        if (answerValue.includes('compensation')) {
          interests.push('CYBERSECURITY', 'DATA_SCIENCE');
        }
      }
    }
    
    if (questionKey === 'desired_outcomes_work_life') {
      if (answerValue === 'very_important' || answerValue === 'critical') {
        interests.push('DIGITAL_MARKETING', 'WEB_DEVELOPMENT');
      }
    }
    
    // Add more mapping logic based on your assessment questions
  });
  
  // Remove duplicates and return
  return [...new Set(interests)];
}

async function getCareerPathSuggestions(assessmentId: string) {
  try {
    // This would typically use your existing career suggestion logic
    // For now, return a simple implementation
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
      include: { responses: true }
    });

    if (!assessment) return [];

    // Simple logic - in a real implementation, you'd use your suggestion rules
    const careerPaths = await prisma.careerPath.findMany({
      where: { isActive: true },
      take: 3
    });

    return careerPaths;
  } catch (error) {
    console.error('Error getting career path suggestions:', error);
    return [];
  }
}
