import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Temporarily bypass authentication for testing
    const testUserId = 'cmbme6uay0000sbkt36tn57zd';

    // const session = await getServerSession(authOptions);

    // if (!session || !session.user?.id) {
    //   return NextResponse.json(
    //     { success: false, error: 'Unauthorized' },
    //     { status: 401 }
    //   );
    // }

    const { searchParams } = new URL(request.url);
    const resourceId = searchParams.get('resourceId');

    if (resourceId) {
      // Get progress for specific resource
      const progress = await prisma.userLearningProgress.findUnique({
        where: {
          userId_resourceId: {
            userId: testUserId,
            resourceId: resourceId
          }
        },
        include: {
          resource: {
            select: {
              id: true,
              title: true,
              url: true
            }
          }
        }
      });

      return NextResponse.json({
        success: true,
        data: progress
      });
    } else {
      // Get all progress for user
      const allProgress = await prisma.userLearningProgress.findMany({
        where: {
          userId: testUserId
        },
        include: {
          resource: {
            select: {
              id: true,
              title: true,
              url: true,
              category: true,
              skillLevel: true
            }
          }
        },
        orderBy: {
          updatedAt: 'desc'
        }
      });

      return NextResponse.json({
        success: true,
        data: allProgress
      });
    }

  } catch (error) {
    console.error('Error fetching learning progress:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch learning progress' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { resourceId, status, notes, rating, review } = body;

    if (!resourceId || !status) {
      return NextResponse.json(
        { success: false, error: 'Resource ID and status are required' },
        { status: 400 }
      );
    }

    // Validate status
    const validStatuses = ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'BOOKMARKED'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Validate rating if provided
    if (rating && (rating < 1 || rating > 5)) {
      return NextResponse.json(
        { success: false, error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    const progress = await prisma.userLearningProgress.upsert({
      where: {
        userId_resourceId: {
          userId: session.user.id,
          resourceId: resourceId
        }
      },
      update: {
        status: status,
        notes: notes || undefined,
        rating: rating || undefined,
        review: review || undefined,
        completedAt: status === 'COMPLETED' ? new Date() : undefined,
        updatedAt: new Date()
      },
      create: {
        userId: session.user.id,
        resourceId: resourceId,
        status: status,
        notes: notes || undefined,
        rating: rating || undefined,
        review: review || undefined,
        completedAt: status === 'COMPLETED' ? new Date() : undefined
      },
      include: {
        resource: {
          select: {
            id: true,
            title: true,
            url: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: progress
    });

  } catch (error) {
    console.error('Error updating learning progress:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update learning progress' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const resourceId = searchParams.get('resourceId');

    if (!resourceId) {
      return NextResponse.json(
        { success: false, error: 'Resource ID is required' },
        { status: 400 }
      );
    }

    await prisma.userLearningProgress.delete({
      where: {
        userId_resourceId: {
          userId: session.user.id,
          resourceId: resourceId
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Progress deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting learning progress:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete learning progress' },
      { status: 500 }
    );
  }
}
