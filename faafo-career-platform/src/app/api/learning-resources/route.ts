import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const skillLevel = searchParams.get('skillLevel');
    const type = searchParams.get('type');
    const cost = searchParams.get('cost');

    const where: any = {
      isActive: true,
    };

    if (category && category !== 'all') {
      where.category = category.toUpperCase();
    }

    if (skillLevel && skillLevel !== 'all') {
      where.skillLevel = skillLevel.toUpperCase();
    }

    if (type && type !== 'all') {
      where.type = type.toUpperCase();
    }

    if (cost && cost !== 'all') {
      where.cost = cost.toUpperCase();
    }

    const resources = await prisma.learningResource.findMany({
      where,
      orderBy: [
        { category: 'asc' },
        { skillLevel: 'asc' },
        { title: 'asc' }
      ],
      include: {
        careerPaths: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        skills: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: resources,
      count: resources.length
    });

  } catch (error) {
    console.error('Error fetching learning resources:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch learning resources' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      title,
      description,
      url,
      type,
      category,
      skillLevel,
      author,
      duration,
      cost = 'FREE',
      format
    } = body;

    // Validate required fields
    if (!title || !description || !url || !type || !category || !skillLevel || !format) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields' 
        },
        { status: 400 }
      );
    }

    const resource = await prisma.learningResource.create({
      data: {
        title,
        description,
        url,
        type: type.toUpperCase(),
        category: category.toUpperCase(),
        skillLevel: skillLevel.toUpperCase(),
        author,
        duration,
        cost: cost.toUpperCase(),
        format: format.toUpperCase()
      }
    });

    return NextResponse.json({
      success: true,
      data: resource
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating learning resource:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create learning resource' 
      },
      { status: 500 }
    );
  }
}
