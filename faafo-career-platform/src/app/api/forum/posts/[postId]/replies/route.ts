import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

// POST handler to create a reply to a forum post
export async function POST(
  request: Request,
  { params }: { params: { postId: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { content } = await request.json();
    const { postId } = params;

    if (!content) {
      return NextResponse.json({ error: 'Content is required' }, { status: 400 });
    }

    if (content.length > 2000) {
      return NextResponse.json({ error: 'Reply content must be 2000 characters or less' }, { status: 400 });
    }

    // Check if the post exists
    const post = await prisma.forumPost.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return NextResponse.json({ error: 'Post not found' }, { status: 404 });
    }

    const newReply = await prisma.forumReply.create({
      data: {
        content: content.trim(),
        authorId: session.user.id,
        postId: postId,
      },
      include: {
        author: {
          select: {
            id: true,
            email: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json(newReply, { status: 201 });
  } catch (error) {
    console.error('Error creating forum reply:', error);
    return NextResponse.json({ error: 'Failed to create forum reply' }, { status: 500 });
  }
}
