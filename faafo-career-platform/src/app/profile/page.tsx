'use client';

import { useState, useEffect, FormEvent } from 'react';
import { useSession } from 'next-auth/react';

interface ProfileData {
  id?: string;
  bio?: string;
  profilePictureUrl?: string;
  socialMediaLinks?: { [key: string]: string }; 
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (status === 'authenticated') {
      fetchProfile();
    } else if (status === 'unauthenticated') {
      // Redirect or show message if not logged in
      // For now, just stop loading and show nothing or a message
      setIsLoading(false);
      // router.push('/login'); // Example redirect
    }
  }, [status]);

  const fetchProfile = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/profile');
      if (!res.ok) {
        throw new Error(`Failed to fetch profile: ${res.statusText} (status: ${res.status})`);
      }
      const data: ProfileData = await res.json();
      setProfile(data);
    } catch (err: unknown) {
      console.error(err);
      if (err instanceof Error) {
        setError(err.message || 'Failed to load profile.');
      } else {
        setError('An unknown error occurred while loading profile.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async (formData: ProfileData): Promise<{ success: boolean; message?: string }> => {
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });
      if (!res.ok) {
        // Try to parse error message from response, otherwise use statusText
        let errorMessage = `Failed to update profile: ${res.statusText} (status: ${res.status})`;
        try {
          const errorData = await res.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          // Ignore if response is not JSON or parsing fails, variable 'e' is not needed
        }
        throw new Error(errorMessage);
      }
      const updatedProfile: ProfileData = await res.json();
      setProfile(updatedProfile);
      return { success: true, message: 'Profile updated successfully!' };
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred while saving profile.';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  if (status === 'loading' || isLoading) {
    return <p>Loading...</p>;
  }

  if (status === 'unauthenticated') {
    return <p>Please log in to view your profile.</p>; // Or redirect
  }

  if (error) {
    return (
      <div>
        <p role="alert">Error: {error}</p>
        <button onClick={fetchProfile} className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          Try Again
        </button>
      </div>
    );
  }

  if (!profile) {
    return <p>No profile data found.</p>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Your Profile</h1>
      {session?.user?.email && <p className="mb-2">Email: {session.user.email}</p>}
      <ProfileForm initialData={profile} onSave={handleSave} />
    </div>
  );
}

interface ProfileFormProps {
  initialData: ProfileData;
  onSave: (data: ProfileData) => Promise<{ success: boolean; message?: string }>;
}

function ProfileForm({ initialData, onSave }: ProfileFormProps) {
  const [formData, setFormData] = useState<ProfileData>(initialData);
  const [formFeedback, setFormFeedback] = useState<{ type: 'success' | 'error'; content: string } | null>(null);

  useEffect(() => {
    setFormData(initialData);
    setFormFeedback(null); // Clear feedback when initialData changes
  }, [initialData]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  
  const handleSocialLinkChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    // Assuming name is something like "socialMediaLinks.website"
    const key = name.split('.')[1]; 
    setFormData(prev => ({
      ...prev,
      socialMediaLinks: { ...(prev.socialMediaLinks || {}), [key]: value } 
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setFormFeedback(null); // Clear previous feedback
    const result = await onSave(formData);
    if (result.success) {
      setFormFeedback({ type: 'success', content: result.message || 'Profile saved successfully!' });
    } else {
      setFormFeedback({ type: 'error', content: result.message || 'Failed to save profile.' });
    }
  };

  return (
    <form 
      onSubmit={handleSubmit} 
      className="space-y-6"
      aria-describedby={formFeedback ? "profile-form-feedback" : undefined}
    >
      <div>
        <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Bio</label>
        <textarea
          id="bio"
          name="bio"
          rows={3}
          className="mt-1 block w-full appearance-none border border-gray-200 dark:border-gray-700 rounded-md py-2 px-3 text-gray-700 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          value={formData.bio || ''}
          onChange={handleChange}
        />
      </div>
      <div>
        <label htmlFor="profilePictureUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Profile Picture URL</label>
        <input
          type="url"
          id="profilePictureUrl"
          name="profilePictureUrl"
          value={formData.profilePictureUrl || ''}
          onChange={handleChange}
          className="mt-1 block w-full appearance-none border border-gray-200 dark:border-gray-700 rounded-md py-2 px-3 text-gray-700 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      <div>
        <label htmlFor="socialMediaLinks.website" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Website URL (Social Link Example)</label>
        <input
          type="url"
          id="socialMediaLinks.website"
          name="socialMediaLinks.website" 
          value={formData.socialMediaLinks?.website || ''}
          onChange={handleSocialLinkChange} 
          className="mt-1 block w-full appearance-none border border-gray-200 dark:border-gray-700 rounded-md py-2 px-3 text-gray-700 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      {/* Add more social media link inputs here as needed */}
      
      {/* Display Form Feedback */}
      {formFeedback && formFeedback.type === 'error' && (
        <p
          id="profile-form-feedback"
          className="text-sm text-red-600"
          role="alert"
        >
          {formFeedback.content}
        </p>
      )}
      {formFeedback && formFeedback.type === 'success' && (
        <p
          id="profile-form-feedback"
          className="text-sm text-green-600"
          role="status"
        >
          {formFeedback.content}
        </p>
      )}

      <button type="submit" className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        Save Profile
      </button>

      <div className="mt-4 text-center text-xs text-gray-500 dark:text-gray-400">
        By updating your profile, you acknowledge our <a href="/terms-of-service" className="underline hover:text-blue-600">Terms of Service</a> and <a href="/privacy-policy" className="underline hover:text-blue-600">Privacy Policy</a>.
      </div>
    </form>
  );
} 