// Define types for questions and steps
export interface Option {
  value: string;
  label: string;
}

export interface BaseQuestion {
  key: string;
  text: string;
  description?: string;
}

export interface MCQuestion extends BaseQuestion {
  type: 'multipleChoice';
  options: Option[];
  allowMultiple?: boolean;
  required?: boolean; // Added for server-side validation consistency
}

export interface ScQuestion extends BaseQuestion {
  type: 'scale';
  minLabel: string;
  maxLabel: string;
  numberOfSteps: number;
  required?: boolean; // Added for server-side validation consistency
}

export type Question = MCQuestion | ScQuestion;

export interface AssessmentStep {
  step: number;
  title: string;
  questions: Question[];
}

// --- Sample Assessment Definition ---
export const assessmentDefinition: AssessmentStep[] = [
  {
    step: 1,
    title: 'Understanding Your Current Situation',
    questions: [
      {
        key: 'dissatisfaction_triggers',
        text: 'What are the primary triggers for your current job dissatisfaction?',
        description: 'Select all that apply.',
        type: 'multipleChoice',
        allowMultiple: true,
        required: true, // Mark as required
        options: [
          { value: 'work_life_balance', label: 'Poor Work-Life Balance' },
          { value: 'lack_of_growth', label: 'Lack of Growth Opportunities' },
          { value: 'compensation', label: 'Inadequate Compensation' },
          { value: 'company_culture', label: 'Negative Company Culture' },
          { value: 'management', label: 'Issues with Management' },
          { value: 'work_meaningless', label: 'Work Feels Meaningless' },
          { value: 'other', label: 'Other (please specify if possible in thoughts later)' },
        ],
      },
    ],
  },
  {
    step: 2,
    title: 'Defining Your Desired Outcomes',
    questions: [
      {
        key: 'financial_comfort',
        text: 'On a scale of 1 to 5, how would you rate your current financial comfort regarding a career change?',
        description: '1 = Very anxious, 5 = Very comfortable',
        type: 'scale',
        minLabel: 'Anxious',
        maxLabel: 'Comfortable',
        numberOfSteps: 5,
        required: true, // Mark as required
      },
      {
        key: 'desired_outcomes_work_life',
        text: 'How important is improved work-life balance in your next role?',
        type: 'multipleChoice',
        options: [
          { value: 'not_important', label: 'Not Important' },
          { value: 'somewhat_important', label: 'Somewhat Important' },
          { value: 'very_important', label: 'Very Important' },
          { value: 'critical', label: 'Critical' },
        ],
      },
    ],
  },
  // Add more steps for skills, etc.
];
// --- End Sample Assessment Definition ---

// Helper function to get all questions in a flat array with their required status
export const getAllQuestions = (): (Question & { step: number })[] => {
  return assessmentDefinition.flatMap(step => 
    step.questions.map(q => ({ ...q, step: step.step }))
  );
};

export const getQuestionByKey = (key: string): (Question & { step: number }) | undefined => {
  return getAllQuestions().find(q => q.key === key);
}; 