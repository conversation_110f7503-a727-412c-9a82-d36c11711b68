import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const learningResourcesData = [
  // Cybersecurity Resources - Beginner
  {
    title: 'Ethical Hacking Essentials (E|HE)',
    description: 'Strong foundations in ethical hacking and penetration testing for entry-level careers',
    url: 'https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'BEGINNER',
    author: 'EC-Council',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Network Defense Essentials (N|DE)',
    description: 'Fundamentals of network security, protocols, controls, and identity/access management',
    url: 'https://www.eccouncil.org/train-certify/network-defense-essentials-nde/',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'BEGINNER',
    author: 'EC-Council',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'CISA Learning',
    description: 'Government-backed cybersecurity training from beginner to advanced levels',
    url: 'https://www.cisa.gov/cybersecurity-training-exercises',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'BEGINNER',
    author: 'CISA',
    duration: 'Various modules',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Cybersecurity Fundamentals',
    description: 'Learn terminology, roles, concepts like encryption, cryptography, and attacker tactics',
    url: 'https://skillsbuild.org/students/course-catalog/cybersecurity',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'BEGINNER',
    author: 'IBM SkillsBuild',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Cybersecurity Resources - Intermediate
  {
    title: 'Digital Forensics Essentials (D|FE)',
    description: 'Steps, practices, and methodologies for digital forensics investigations',
    url: 'https://www.eccouncil.org/train-certify/digital-forensics-essentials-dfe/',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'INTERMEDIATE',
    author: 'EC-Council',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Cloud Security Essentials (C|SE)',
    description: 'Fundamentals of cloud computing and securing identities, data, and applications',
    url: 'https://www.eccouncil.org/train-certify/cloud-security-essentials-cse/',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'INTERMEDIATE',
    author: 'EC-Council',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Certified in Cybersecurity (CC)',
    description: 'Entry-level certification covering security principles, business continuity, and incident response',
    url: 'https://www.isc2.org/landing/1mcc',
    type: 'CERTIFICATION',
    category: 'CYBERSECURITY',
    skillLevel: 'INTERMEDIATE',
    author: 'ISC2',
    duration: 'Self-paced training',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Data Science Resources - Beginner
  {
    title: 'Data Science: Machine Learning',
    description: 'Basics of machine learning, cross-validation, popular algorithms, and avoiding overtraining',
    url: 'https://pll.harvard.edu/course/data-science-machine-learning',
    type: 'COURSE',
    category: 'DATA_SCIENCE',
    skillLevel: 'BEGINNER',
    author: 'Harvard University',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Machine Learning Crash Course',
    description: 'Fast-paced introduction with animated videos, interactive visualizations, and hands-on practice',
    url: 'https://developers.google.com/machine-learning/crash-course',
    type: 'COURSE',
    category: 'DATA_SCIENCE',
    skillLevel: 'BEGINNER',
    author: 'Google',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'INTERACTIVE'
  },
  {
    title: 'Introduction to Data Science',
    description: 'Foundational data science skills led by tech experts',
    url: 'https://skillsbuild.org/students/course-catalog/data-science',
    type: 'COURSE',
    category: 'DATA_SCIENCE',
    skillLevel: 'BEGINNER',
    author: 'IBM SkillsBuild',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Blockchain Resources - Beginner
  {
    title: 'Blockchain Basics',
    description: 'Introduction to blockchain technology, cryptocurrency, and smart contracts',
    url: 'https://www.coursera.org/learn/blockchain-basics',
    type: 'COURSE',
    category: 'BLOCKCHAIN',
    skillLevel: 'BEGINNER',
    author: 'University at Buffalo',
    duration: 'Self-paced',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },
  {
    title: 'Blockchain Fundamentals',
    description: 'Comprehensive introduction to blockchain technology and its applications',
    url: 'https://www.edx.org/professional-certificate/uc-berkeleyx-blockchain-fundamentals',
    type: 'CERTIFICATION',
    category: 'BLOCKCHAIN',
    skillLevel: 'BEGINNER',
    author: 'UC Berkeley',
    duration: 'Self-paced',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },

  // Project Management Resources - Beginner
  {
    title: 'Project Management Foundations',
    description: 'Introduction to project management principles and methodologies',
    url: 'https://www.linkedin.com/learning/project-management-foundations-2019',
    type: 'COURSE',
    category: 'PROJECT_MANAGEMENT',
    skillLevel: 'BEGINNER',
    author: 'LinkedIn Learning',
    duration: 'Video course',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },
  {
    title: 'Introduction to Project Management',
    description: 'Foundational concepts in project management',
    url: 'https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0',
    type: 'COURSE',
    category: 'PROJECT_MANAGEMENT',
    skillLevel: 'BEGINNER',
    author: 'Open University',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Digital Marketing Resources - Beginner
  {
    title: 'Fundamentals of Digital Marketing',
    description: 'Comprehensive introduction to digital marketing concepts and tools',
    url: 'https://learndigital.withgoogle.com/digitalgarage/course/digital-marketing',
    type: 'COURSE',
    category: 'DIGITAL_MARKETING',
    skillLevel: 'BEGINNER',
    author: 'Google Digital Garage',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Social Media Marketing',
    description: 'Introduction to social media marketing strategies and platforms',
    url: 'https://academy.hubspot.com/courses/social-media',
    type: 'COURSE',
    category: 'DIGITAL_MARKETING',
    skillLevel: 'BEGINNER',
    author: 'HubSpot Academy',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // AI Resources - Beginner
  {
    title: 'AI For Everyone',
    description: 'Non-technical introduction to AI concepts and applications',
    url: 'https://www.coursera.org/learn/ai-for-everyone',
    type: 'COURSE',
    category: 'ARTIFICIAL_INTELLIGENCE',
    skillLevel: 'BEGINNER',
    author: 'DeepLearning.AI',
    duration: 'Self-paced',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },
  {
    title: 'Elements of AI',
    description: 'Introduction to AI concepts and their practical applications',
    url: 'https://www.elementsofai.com/',
    type: 'COURSE',
    category: 'ARTIFICIAL_INTELLIGENCE',
    skillLevel: 'BEGINNER',
    author: 'University of Helsinki',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'INTERACTIVE'
  }
];

async function seedLearningResources() {
  console.log('🌱 Seeding learning resources...');

  try {
    // Create learning resources
    for (const resource of learningResourcesData) {
      await prisma.learningResource.upsert({
        where: { url: resource.url },
        update: resource,
        create: resource,
      });
    }

    console.log(`✅ Successfully seeded ${learningResourcesData.length} learning resources`);
  } catch (error) {
    console.error('❌ Error seeding learning resources:', error);
    throw error;
  }
}

async function main() {
  await seedLearningResources();
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
